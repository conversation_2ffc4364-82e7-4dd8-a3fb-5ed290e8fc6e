import { useMemo, useRef, useCallback, useEffect } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

// Suppress ReactQuill findDOMNode warning - this is a known issue with ReactQuill v2.0.0
// The warning doesn't affect functionality and will be resolved in future ReactQuill updates
const suppressReactQuillWarnings = () => {
  const originalError = console.error;
  console.error = (...args) => {
    if (
      typeof args[0] === "string" &&
      args[0].includes("findDOMNode is deprecated")
    ) {
      return;
    }
    originalError(...args);
  };
};

export interface RichTextEditorProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  className?: string;
  height?: string;
  onImageUpload?: (file: File) => Promise<string>; // Optional custom image upload handler
}

const RichTextEditor = ({
  label,
  placeholder,
  value = "",
  onChange,
  required = false,
  disabled = false,
  error,
  className = "",
  height = "200px",
  onImageUpload,
}: RichTextEditorProps) => {
  const quillRef = useRef<ReactQuill>(null);

  // Debug effect to check if component is mounting properly
  useEffect(() => {
    console.log("RichTextEditor mounted with props:", {
      label,
      placeholder,
      value: value?.length || 0,
      disabled,
      height,
    });
  }, []);

  // Handle image upload
  const imageHandler = useCallback(() => {
    const input = document.createElement("input");
    input.setAttribute("type", "file");
    input.setAttribute("accept", "image/*");
    input.click();

    input.onchange = async () => {
      const file = input.files?.[0];
      if (file) {
        const quill = quillRef.current?.getEditor();
        if (quill) {
          const range = quill.getSelection();

          if (onImageUpload) {
            // Use custom upload handler (e.g., cloud storage)
            try {
              const imageUrl = await onImageUpload(file);
              quill.insertEmbed(range?.index || 0, "image", imageUrl);
            } catch (error) {
              console.error("Image upload failed:", error);
              // Fallback to base64
              const reader = new FileReader();
              reader.onload = () => {
                quill.insertEmbed(range?.index || 0, "image", reader.result);
              };
              reader.readAsDataURL(file);
            }
          } else {
            // Convert to base64 for simple implementation
            const reader = new FileReader();
            reader.onload = () => {
              quill.insertEmbed(range?.index || 0, "image", reader.result);
            };
            reader.readAsDataURL(file);
          }
        }
      }
    };
  }, [onImageUpload]);
  const modules = useMemo(
    () => ({
      toolbar: {
        container: [
          [{ header: [1, 2, 3, false] }],
          ["bold", "italic", "underline", "strike"],
          [{ list: "ordered" }, { list: "bullet" }],
          [{ indent: "-1" }, { indent: "+1" }],
          ["link", "image"],
          ["clean"],
        ],
        handlers: {
          image: imageHandler,
        },
      },
    }),
    [imageHandler]
  );

  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "list",
    "bullet",
    "indent",
    "link",
    "image",
  ];

  return (
    <div className={`flex flex-col w-full my-2 ${className}`}>
      {label && (
        <label className="text-sm font-medium text-gray-700 mb-1 shrink-0">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <div
        className="rich-text-editor-wrapper"
        style={{ "--editor-height": height } as React.CSSProperties}
      >
        <ReactQuill
          ref={quillRef}
          theme="snow"
          value={value}
          onChange={(content) => {
            try {
              onChange?.(content);
            } catch (error) {
              console.error("RichTextEditor onChange error:", error);
            }
          }}
          modules={modules}
          formats={formats}
          placeholder={placeholder}
          readOnly={disabled}
        />
      </div>
      {error && <span className="text-red-500 text-sm mt-1">{error}</span>}
    </div>
  );
};

export default RichTextEditor;
